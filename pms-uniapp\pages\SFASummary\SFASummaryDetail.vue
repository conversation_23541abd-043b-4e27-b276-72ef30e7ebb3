<template>
    <view>
        <cu-custom :bgColor="NavBarColor" isBack>
            <block slot="backText">返回</block>
            <block slot="content">{{ userName }}</block>
            <block slot="right">
                <uni-datetime-picker type="date" return-type="string" v-model="currentDate" @change="onDateChange"
                    :border="false" :clear-icon="false">
                    <view class="date-picker-trigger">
                        {{ currentDate }}
                    </view>
                </uni-datetime-picker>
            </block>
        </cu-custom>

        <view class="container">
            <!-- 签到签退记录模块 -->
            <view class="attendance-status-section">
                <view class="status-title">
                    <text class="cuIcon-calendar text-blue"></text>
                    <text class="status-title-text">今日考勤状态</text>
                </view>
                <view class="status-content">
                    <view class="status-date">{{ currentDate }}</view>
                    <view class="status-items">
                        <view class="status-item">
                            <view class="status-label">签到时间</view>
                            <view class="status-value-wrapper">
                                <view class="status-value" :class="{ 'has-value': signInInfo.checkInTime }">
                                    {{ signInInfo.checkInTime || '未签到' }}
                                </view>
                                <view class="attendance-photo-list" v-if="signInInfo.pic">
                                    <image :src="getImageUrl(signInInfo.pic)" class="attendance-photo" mode="aspectFill"
                                        @tap="previewImage(signInInfo.pic)"></image>
                                </view>
                            </view>
                        </view>
                        <view class="status-item">
                            <view class="status-label">签退时间</view>
                            <view class="status-value-wrapper">
                                <view class="status-value" :class="{ 'has-value': signOutInfo.checkInTime }">
                                    {{ signOutInfo.checkInTime || '未签退' }}
                                </view>
                                <view class="attendance-photo-list" v-if="signOutInfo.pic">
                                    <image :src="getImageUrl(signOutInfo.pic)" class="attendance-photo"
                                        mode="aspectFill" @tap="previewImage(signOutInfo.pic)"></image>
                                </view>
                            </view>
                        </view>
                        <view class="status-item" v-if="workDuration">
                            <view class="status-label">工作时长</view>
                            <view class="status-value has-value">{{ workDuration }}</view>
                        </view>
                    </view>
                    <view class="status-summary">
                        <text class="status-badge" :class="getStatusClass()">{{ attendanceStatus }}</text>
                    </view>
                </view>
            </view>

            <!-- 今日工作日报模块 -->
            <view class="card">
                <view class="card-header">
					<view class="flex align-center">
						<text class="cuIcon-group text-blue margin-right-xs"></text>
						<text>今日工作日报</text>
					</view>
					<text v-if="detailInfo.dayReport" class="cuIcon-add text-blue" @tap="showAddCommentModal">评价</text>
				</view>
                <view class="card-body">
                    <view v-if="!detailInfo.dayReport" class="empty-state">
                        <text class="cuIcon-edit text-gray"></text>
                        <text class="empty-text">暂无工作日报</text>
                    </view>
                    <view v-else class="report-content">
                        <view class="report-item">
                            <view class="report-label">今日工作总结</view>
                            <view class="report-value">{{ detailInfo.dayReport.todayWorkSummary }}</view>
                        </view>
                        <view class="report-item">
                            <view class="report-label">明日工作计划</view>
                            <view class="report-value">{{ detailInfo.dayReport.tomorrowWorkPlan }}</view>
                        </view>
                        <view class="report-item">
                            <view class="report-label">需协调事项</view>
                            <view class="report-value">{{ detailInfo.dayReport.coordinateMatter || '无' }}</view>
                        </view>

                        <!-- 评论列表区域 -->
                        <view class="comment-section">
                            <view class="comment-title">点评列表</view>
                            <scroll-view class="comment-list" scroll-y="true">
                                <view v-if="commentList.length === 0" class="empty-comment">
                                    <text class="cuIcon-message text-gray"></text>
                                    <text class="empty-text">暂无评论</text>
                                </view>
                                <view v-else>
                                    <view v-for="(comment, index) in commentList" :key="comment.id" class="comment-item">
                                        <view class="comment-content">{{ comment.content }}</view>
                                        <view class="comment-meta">
                                            <text class="comment-time">{{ comment.realname }}  {{ comment.createTime }}</text>
                                            <!-- <view class="comment-actions">
                                                <button class="cu-btn sm line-blue" @click="editComment(comment)">编辑</button>
                                                <button class="cu-btn sm line-red margin-left-xs" @click="deleteComment(comment.id)">删除</button>
                                            </view> -->
                                        </view>
                                    </view>
                                </view>
                            </scroll-view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 今日拜访计划列表模块 -->
            <view class="card">
                <view class="card-header">
                    <view class="flex align-center">
                        <text class="cuIcon-group text-blue margin-right-xs"></text>
                        <text>今日拜访计划</text>
                    </view>
                </view>
                <view class="card-body">
                    <view v-if="!detailInfo.visitPlanVOList || detailInfo.visitPlanVOList.length === 0"
                        class="empty-state">
                        <text class="cuIcon-calendar text-gray"></text>
                        <text class="empty-text">暂无拜访计划</text>
                    </view>
                    <view v-else>
                        <view v-for="(visit, index) in detailInfo.visitPlanVOList" :key="index" class="visit-item">
                            <view class="visit-avatar">
                                {{ visit.customerName ? visit.customerName.charAt(0) : visit.type.charAt(0) }}
                            </view>
                            <view class="visit-info">
                                <view class="visit-name">{{ visit.customerName || '未知客户' }}</view>
                                <view class="visit-meta">
                                    <text class="cuIcon-time text-gray margin-right-xs"></text>
                                    <text>{{ visit.visitTime }}</text>
                                    <text class="cuIcon-location text-gray margin-left-sm margin-right-xs"></text>
                                    <text>{{ visit.visitAddress }}</text>
                                </view>
                                <view class="visit-purpose margin-top-xs">
                                    <text class="text-sm text-gray">{{ visit.visitPurpose }}</text>
                                </view>
                                <view class="visit-business-info margin-top-xs"
                                    v-if="visit.distributorName || visit.systemName">
                                    <view v-if="visit.distributorName" class="business-item">
                                        <text class="cuIcon-group text-blue margin-right-xs"></text>
                                        <text class="business-label">分销商:</text>
                                        <text class="business-value">{{ visit.distributorName }}</text>
                                    </view>
                                    <view v-if="visit.systemName" class="business-item margin-top-xs">
                                        <text class="cuIcon-settings text-orange margin-right-xs"></text>
                                        <text class="business-label">体系:</text>
                                        <text class="business-value">{{ visit.systemName }}</text>
                                    </view>
                                </view>
                            </view>
                            <view class="visit-actions">
                                <view class="visit-status" :class="getVisitStatusClass(visit.visitStatus)">
                                    {{ visit.visitStatusName }}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 地图显示与标点功能 -->
            <view class="card">
                <view class="card-header">
                    <view class="flex align-center">
                        <text class="cuIcon-location text-orange margin-right-xs"></text>
                        <text>今日工作轨迹</text>
                    </view>
                </view>
                <view class="card-body">
                    <view class="map-container">
                        <map id="workTrackMap" :longitude="mapCenter.longitude" :latitude="mapCenter.latitude"
                            :scale="mapScale" :markers="mapMarkers" :polyline="mapPolyline" :show-location="true"
                            class="work-map">
                        </map>
                    </view>
                    <!-- <view class="track-info">
                        <view class="track-data">
                            <view class="track-value">{{ mapMarkers.length }}</view>
                            <view class="track-label">标记点</view>
                        </view>
                        <view class="track-data">
                            <view class="track-value">{{ detailInfo.clockInList ? detailInfo.clockInList.length : 0 }}</view>
                            <view class="track-label">签到记录</view>
                        </view>
                        <view class="track-data">
                            <view class="track-value">{{ detailInfo.visitPlanVOList ? detailInfo.visitPlanVOList.length : 0 }}</view>
                            <view class="track-label">拜访计划</view>
                        </view>
                    </view> -->
                </view>
            </view>
        </view>

        <!-- 评论弹框 -->
        <view v-if="showCommentModal" class="modal-overlay" @click="hideCommentModal">
            <view class="modal-content" @click.stop>
                <view class="modal-header">
                    <text class="modal-title">{{ isEditMode ? '编辑评论' : '新增评论' }}</text>
                    <text class="cuIcon-close text-gray" @click="hideCommentModal"></text>
                </view>
                <view class="modal-body">
                    <view class="form-group">
                        <textarea
                            v-model="commentForm.content"
                            placeholder="请输入评论内容..."
                            class="comment-textarea"
                            maxlength="500"
                            :show-confirm-bar="false"
                        ></textarea>
                        <view class="char-count">{{ commentForm.content.length }}/500</view>
                    </view>
                </view>
                <view class="modal-footer">
                    <button class="cu-btn line-gray" @click="hideCommentModal">取消</button>
                    <button class="cu-btn bg-green margin-left-sm" @click="submitComment" :disabled="!commentForm.content.trim()">
                        {{ isEditMode ? '保存' : '提交' }}
                    </button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import configService from '@/common/service/config.service.js';

export default {
    data() {
        return {
            userName: "",
            userCode: "",
            detailInfo: {},
            currentDate: '',
            // 地图相关
            mapCenter: {
                longitude: 104.195397, // 默认经度
                latitude: 35.86166     // 默认纬度
            },
            mapScale: 12,
            mapMarkers: [],
            mapPolyline: [],
            mapPolyline: [],
            // 评论相关
            commentList: [],
            showCommentModal: false,
            isEditMode: false,
            commentForm: {
                id: null,
                content: '',
                reportId: null
            }
        }
    },

    async onLoad(options) {
		const urlParams=this.$Route.query
		console.log("🚀 ~ onLoad ~ urlParams:", urlParams)
        this.userName = urlParams.name;
        this.userCode = urlParams.userCode;
        this.currentDate = urlParams.date;
        try {
            const result = await this.$http.get(`/pm/clockIn/overviewDetail?userCode=${this.userCode}&date=${this.currentDate}`);
            if (result.data.code == 200) {
                this.detailInfo = result.data.result;
                console.log("🚀 ~ this.detailInfo", this.detailInfo)
                // 初始化地图标记
                this.initMapMarkers();
                // 获取评论列表
                if (this.detailInfo.dayReport && this.detailInfo.dayReport.id) {
                    this.loadCommentList();
                }
            } else {
                uni.showToast({
                    title: result.message || '获取详情失败',
                    icon: 'none'
                });
            }
        } catch (error) {
            console.error('获取详情失败:', error);
            uni.showToast({
                title: '获取详情失败',
                icon: 'none'
            });
        }
    },

    computed: {
        // 签到信息
        signInInfo() {
            if (!this.detailInfo.clockInList) return {};
            return this.detailInfo.clockInList.find(item => item.type === 1) || {};
        },

        // 签退信息
        signOutInfo() {
            if (!this.detailInfo.clockInList) return {};
            return this.detailInfo.clockInList.find(item => item.type === 2) || {};
        },

        // 工作时长
        workDuration() {
            if (!this.signInInfo.checkInTime || !this.signOutInfo.checkInTime) {
                return '';
            }

            const signInTime = new Date(`${this.currentDate} ${this.signInInfo.checkInTime.split(' ')[1]}`);
            const signOutTime = new Date(`${this.currentDate} ${this.signOutInfo.checkInTime.split(' ')[1]}`);

            const diffMs = signOutTime - signInTime;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

            return `${diffHours}小时${diffMinutes}分钟`;
        },

        // 考勤状态
        attendanceStatus() {
            if (!this.signInInfo.checkInTime && !this.signOutInfo.checkInTime) {
                return '未签到';
            } else if (this.signInInfo.checkInTime && !this.signOutInfo.checkInTime) {
                return '已签到';
            } else if (this.signInInfo.checkInTime && this.signOutInfo.checkInTime) {
                return '已签退';
            }
            return '异常';
        }
    },

    methods: {
        // 日期变化事件
        async onDateChange(newDate) {
            console.log('日期变化:', newDate);
            // 显示加载状态
            uni.showLoading({
                title: '加载中...',
                mask: true
            });
            try {
                // 重新获取数据
                const result = await this.$http.get(`/pm/clockIn/overviewDetail?userCode=${this.userCode}&date=${this.currentDate}`);
                if (result.data.code == 200) {
                    this.detailInfo = result.data.result;
                    console.log("🚀 ~ this.detailInfo", this.detailInfo)
                    // 初始化地图标记
                    this.initMapMarkers();
                    // 获取评论列表
                    if (this.detailInfo.dayReport && this.detailInfo.dayReport.id) {
                        this.loadCommentList();
                    } else {
                        this.commentList = [];
                    }
                } else {
                    uni.showToast({
                        title: result.message || '获取详情失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('获取数据失败:', error);
                uni.showToast({
                    title: '获取数据失败',
                    icon: 'none'
                });
            } finally {
                uni.hideLoading();
            }
        },

        // 获取图片完整URL
        getImageUrl(url) {
            if (!url) return '';
            if (url.startsWith('http')) {
                return url;
            }
            const domain = configService.staticDomainURL || '';
            // 移除 domain 尾部的 /
            const cleanDomain = domain.endsWith('/') ? domain.slice(0, -1) : domain;
            // 移除 url 头部的 /
            const cleanUrl = url.startsWith('/') ? url.slice(1) : url;
            if (!cleanDomain) return `/${cleanUrl}`;
            return `${cleanDomain}/${cleanUrl}`;
        },

        // 预览图片
        previewImage(pic) {
            if (!pic) return;
            uni.previewImage({
                urls: [this.getImageUrl(pic)],
                current: 0
            });
        },

        // 获取考勤状态样式类
        getStatusClass() {
            switch (this.attendanceStatus) {
                case '已签退':
                    return 'status-completed';
                case '已签到':
                    return 'status-in-progress';
                case '未签到':
                    return 'status-not-started';
                default:
                    return 'status-error';
            }
        },

        // 获取拜访状态样式类
        getVisitStatusClass(status) {
            switch (status) {
                case '1':
                    return 'status-planned';
                case '2':
                    return 'status-in-progress';
                case '3':
                    return 'status-completed';
                default:
                    return 'status-unknown';
            }
        },

        // 初始化地图标记
        initMapMarkers() {
            this.mapMarkers = [];
            this.mapPolyline = [];
            let hasValidLocation = false;
            let polylinePoints = [];

            // 从markList中获取标记数据
            if (this.detailInfo.markList && this.detailInfo.markList.length > 0) {
                // 按时间排序标记点
                const sortedMarks = this.detailInfo.markList.sort((a, b) => {
                    return new Date(a.time) - new Date(b.time);
                });

                sortedMarks.forEach((mark, index) => {
                    if (mark.longitude && mark.latitude) {
                        // 根据类型设置不同的图标和颜色
                        let iconColor = '#2196F3';
                        let iconPath = '/static/images/icon.png';

                        switch (mark.type) {
                            case '签到':
                                iconColor = '#4CAF50';
                                break;
                            case '签退':
                                iconColor = '#4CAF5A';
                                break;
                            case '客户':
                                iconColor = '#2196F3';
                                break;
                            case '分销商':
                                iconColor = '#FF9800';
                                break;
                            case '体系':
                                iconColor = '#9C27B0';
                                break;
                            case '意向客户':
                                iconColor = '#A91E63';
                                break;
                            default:
                                iconColor = '#9E9E9E';
                        }

                        // 根据类型设置callout位置和内容
                        let calloutConfig = {
                            color: '#ffffff',
                            fontSize: 12,
                            borderRadius: 4,
                            bgColor: iconColor,
                            padding: 8,
                            display: 'ALWAYS'
                        };

                        const marker = {
                            id: `mark_${index}`,
                            longitude: parseFloat(mark.longitude),
                            latitude: parseFloat(mark.latitude),
                            iconPath: iconPath,
                            width: 30,
                            height: 30,
                            title: mark.customerName || mark.type
                        };

                        // 根据类型设置不同的显示方式
                        if (mark.type === '签到') {
                            // 签到使用label显示在上方
                            marker.label = {
                                content: `${mark.customerName || mark.type}: ${mark.time}`,
                                color: '#ffffff',
                                fontSize: 12,
                                borderWidth: 1,
                                borderColor: iconColor,
                                bgColor: iconColor,
                                borderRadius: 4,
                                padding: 6,
                                textAlign: 'center',
                                x: 0,
                                y: -20  // 显示在标点上方
                            };
                        } else if (mark.type === '签退') {
                            // 签退使用label显示在下方
                            marker.label = {
                                content: `${mark.customerName || mark.type}: ${mark.time}`,
                                color: '#ffffff',
                                fontSize: 12,
                                borderWidth: 1,
                                borderColor: iconColor,
                                bgColor: iconColor,
                                borderRadius: 4,
                                padding: 6,
                                textAlign: 'center',
                                x: 0,
                                y: 10   // 显示在标点下方
                            };
                        } else {
                            marker.label = {
                                content: `${mark.customerName || mark.type}: ${mark.time}`,
                                color: '#ffffff',
                                fontSize: 12,
                                borderWidth: 1,
                                borderColor: iconColor,
                                bgColor: iconColor,
                                borderRadius: 4,
                                padding: 6,
                                textAlign: 'center',
                                x: 5,
                                y: 0   // 显示在标点下方
                            };
                        }

                        this.mapMarkers.push(marker);

                        // 添加到连线点数组
                        polylinePoints.push({
                            longitude: parseFloat(mark.longitude),
                            latitude: parseFloat(mark.latitude)
                        });

                        // 设置地图中心点（使用第一个有效位置）
                        if (!hasValidLocation) {
                            this.mapCenter.longitude = marker.longitude;
                            this.mapCenter.latitude = marker.latitude;
                            hasValidLocation = true;
                        }
                    }
                });

                // 生成连线（如果有2个或以上的点）
                if (polylinePoints.length >= 2) {
                    this.mapPolyline = [{
                        points: polylinePoints,
                        color: '#3388FF',
                        width: 4,
                        dottedLine: false,
                        arrowLine: true,
                        borderColor: '#FFFFFF',
                        borderWidth: 2
                    }];
                }
            }
        },

        // 获取评论列表
        async loadCommentList() {
            if (!this.detailInfo.dayReport || !this.detailInfo.dayReport.id) return;

            try {
                const result = await this.$http.get(`/pm/reportComment/list?reportId=${this.detailInfo.dayReport.id}`);
                if (result.data.code === 200) {
                    this.commentList = result.data.result || [];
                } else {
                    console.error('获取评论列表失败:', result.data.message);
                }
            } catch (error) {
                console.error('获取评论列表失败:', error);
            }
        },

        // 显示新增评论弹框
        showAddCommentModal() {
            this.isEditMode = false;
            this.commentForm = {
                id: null,
                content: '',
                reportId: this.detailInfo.dayReport.id
            };
            this.showCommentModal = true;
        },

        // 隐藏评论弹框
        hideCommentModal() {
            this.showCommentModal = false;
            this.commentForm = {
                id: null,
                content: '',
                reportId: null
            };
        },

        // 编辑评论
        editComment(comment) {
            this.isEditMode = true;
            this.commentForm = {
                id: comment.id,
                content: comment.content,
                reportId: this.detailInfo.dayReport.id,
                userCode: comment.userCode
            };
            this.showCommentModal = true;
        },

        // 提交评论
        async submitComment() {
            if (!this.commentForm.content.trim()) {
                uni.showToast({
                    title: '请输入评论内容',
                    icon: 'none'
                });
                return;
            }

            try {
                uni.showLoading({
                    title: this.isEditMode ? '保存中...' : '提交中...',
                    mask: true
                });

                let result;

                if (this.isEditMode) {
                    // 编辑评论
                    const editParams = {
                        id: this.commentForm.id,
                        content: this.commentForm.content.trim(),
                        userCode:this.commentForm.userCode
                    };
                    console.log("🚀 ~ submitComment ~ editParams:", this.commentForm)
                    result = await this.$http.post('/pm/reportComment/edit', editParams);
                } else {
                    // 新增评论
                    const addParams = {
                        reportId: this.commentForm.reportId,
                        content: this.commentForm.content.trim()
                    };
                    result = await this.$http.post('/pm/reportComment/add', addParams);
                }

                if (result.data.code === 200) {
                    uni.showToast({
                        title: this.isEditMode ? '保存成功' : '提交成功',
                        icon: 'success'
                    });
                    this.hideCommentModal();
                    // 重新加载评论列表
                    this.loadCommentList();
                } else {
                    uni.showToast({
                        title: result.data.message || '操作失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('提交评论失败:', error);
                uni.showToast({
                    title: '操作失败',
                    icon: 'none'
                });
            } finally {
                uni.hideLoading();
            }
        },

        // 删除评论
        async deleteComment(commentId) {
            uni.showModal({
                title: '确认删除',
                content: '确定要删除这条评论吗？',
                success: async (res) => {
                    if (res.confirm) {
                        try {
                            uni.showLoading({
                                title: '删除中...',
                                mask: true
                            });

                            const result = await this.$http.post('/pm/reportComment/delete', { id: commentId });

                            if (result.data.code === 200) {
                                uni.showToast({
                                    title: '删除成功',
                                    icon: 'success'
                                });
                                // 重新加载评论列表
                                this.loadCommentList();
                            } else {
                                uni.showToast({
                                    title: result.data.message || '删除失败',
                                    icon: 'none'
                                });
                            }
                        } catch (error) {
                            console.error('删除评论失败:', error);
                            uni.showToast({
                                title: '删除失败',
                                icon: 'none'
                            });
                        } finally {
                            uni.hideLoading();
                        }
                    }
                }
            });
        }
    },
}
</script>

<style lang="scss" scoped>
.container {
    padding: 20upx;
    background-color: #f5f5f5;
    min-height: 100vh;
}

/* 今日考勤状态区域 */
.attendance-status-section {
    background: #ffffff;
    border-radius: 20upx;
    padding: 30upx;
    margin-bottom: 30upx;
    box-shadow: 0 8upx 25upx rgba(0, 0, 0, 0.1);
}

.status-title {
    display: flex;
    align-items: center;
    margin-bottom: 25upx;
    padding-bottom: 15upx;
    border-bottom: 1upx solid #f0f0f0;
}

.status-title text:first-child {
    font-size: 32upx;
    margin-right: 10upx;
}

.status-title-text {
    font-size: 30upx;
    color: #2c3e50;
    font-weight: 500;
}

.status-content {
    display: flex;
    flex-direction: column;
}

.status-date {
    font-size: 26upx;
    color: #7f8c8d;
    text-align: center;
    margin-bottom: 20upx;
}

.status-items {
    display: flex;
    flex-direction: column;
    gap: 15upx;
    margin-bottom: 20upx;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 15upx 20upx;
    background: #f8f9fa;
    border-radius: 12upx;
}

.status-label {
    font-size: 28upx;
    color: #34495e;
    font-weight: 500;
    min-width: 120upx;
}

.status-value-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    flex: 1;
}

.status-value {
    font-size: 26upx;
    color: #95a5a6;
    text-align: right;
    margin-bottom: 10upx;
}

.status-value.has-value {
    color: #2c3e50;
    font-weight: 500;
}

.attendance-photo-list {
    display: flex;
    gap: 10upx;
}

.attendance-photo {
    width: 60upx;
    height: 60upx;
    border-radius: 8upx;
    border: 2upx solid #e0e0e0;
}

.status-summary {
    text-align: center;
    padding-top: 15upx;
    border-top: 1upx solid #f0f0f0;
}

.status-badge {
    display: inline-block;
    padding: 8upx 20upx;
    border-radius: 20upx;
    font-size: 24upx;
    font-weight: 500;
    color: #ffffff;
}

.status-completed {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.status-in-progress {
    background: linear-gradient(135deg, #FF9800, #f57c00);
}

.status-not-started {
    background: linear-gradient(135deg, #9E9E9E, #757575);
}

.status-error {
    background: linear-gradient(135deg, #F44336, #d32f2f);
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    margin-bottom: 30rpx;
    overflow: hidden;
}

.card-header {
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    font-size: 32rpx;
}

.card-body {
    padding: 30rpx;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;
    color: #999;
}

.empty-state text:first-child {
    font-size: 80rpx;
    margin-bottom: 20rpx;
}

.empty-text {
    font-size: 28rpx;
}

/* 工作日报样式 */
.report-content {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.report-item {
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    border-left: 4rpx solid #4CAF50;
}

.report-label {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 10rpx;
    font-weight: 500;
}

.report-value {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
    white-space: pre-wrap;
}

/* 拜访项目样式 */
.visit-item {
    display: flex;
    align-items: flex-start;
    padding: 24rpx 0;
    border-bottom: 1px solid #f0f0f0;
}

.visit-item:last-child {
    border-bottom: none;
}

.visit-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 32rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
}

.visit-info {
    flex: 1;
    min-width: 0;
}

.visit-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8rpx;
}

.visit-meta {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: #7f8c8d;
    margin-bottom: 8rpx;
    flex-wrap: wrap;
}

.visit-purpose {
    font-size: 24rpx;
    color: #95a5a6;
    line-height: 1.4;
}

.visit-business-info {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.business-item {
    display: flex;
    align-items: center;
    font-size: 24rpx;
}

.business-label {
    color: #7f8c8d;
    margin-right: 8rpx;
}

.business-value {
    color: #34495e;
    font-weight: 500;
}

.visit-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    margin-left: 16rpx;
}

.visit-status {
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    font-weight: 500;
    color: white;
    text-align: center;
    min-width: 80rpx;
}

.status-planned {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.status-in-progress {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.status-completed {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.status-unknown {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

/* 地图样式 */
.map-container {
    width: 100%;
    height: 400rpx;
    border-radius: 16rpx;
    overflow: hidden;
    margin-bottom: 20rpx;
}

.work-map {
    width: 100%;
    height: 100%;
}

.track-info {
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0;
    background: #f8f9fa;
    border-radius: 12rpx;
}

.track-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
}

.track-value {
    font-size: 36rpx;
    font-weight: bold;
    color: #2c3e50;
}

.track-label {
    font-size: 24rpx;
    color: #7f8c8d;
}

/* 通用样式 */
.flex {
    display: flex;
}

.align-center {
    align-items: center;
}

.margin-right-xs {
    margin-right: 10rpx;
}

.margin-left-sm {
    margin-left: 20rpx;
}

.margin-top-xs {
    margin-top: 10rpx;
}

.text-sm {
    font-size: 24rpx;
}

.text-gray {
    color: #7f8c8d;
}

.text-blue {
    color: #3498db;
}

.text-green {
    color: #27ae60;
}

.text-orange {
    color: #f39c12;
}

/* 日期选择器样式 */
.date-picker-trigger {
    color: #ffffff;
    font-weight: 500;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    background: rgba(0, 122, 255, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    user-select: none;
}

.date-picker-trigger:active {
    background: rgb(255, 255, 255);
    transform: scale(0.95);
}

/* 覆盖uni-datetime-picker的默认样式 */
.cu-custom ::v-deep .uni-datetime-picker {
    display: inline-block;
}

.cu-custom ::v-deep .uni-datetime-picker .uni-datetime-picker-text {
    color: inherit;
    font-size: inherit;
}

/* 评论相关样式 */
.comment-section {
    margin-top: 30upx;
    border-top: 1px solid #eee;
    padding-top: 20upx;
}

.comment-title {
    font-size: 28upx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20upx;
}

.comment-list {
    height: 400upx;
    background: #f8f9fa;
    border-radius: 10upx;
    padding: 20upx;
}

.empty-comment {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
}

.empty-comment .cuIcon-message {
    font-size: 60upx;
    margin-bottom: 20upx;
}

.empty-comment .empty-text {
    font-size: 26upx;
}

.comment-item {
    background: white;
    border-radius: 10upx;
    padding: 20upx;
    margin-bottom: 20upx;
    box-shadow: 0 2upx 10upx rgba(0, 0, 0, 0.1);
}

.comment-content {
    font-size: 28upx;
    color: #333;
    line-height: 1.6;
    word-break: break-all;
    margin-bottom: 15upx;
}

.comment-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comment-time {
    font-size: 24upx;
    color: #999;
}

.comment-actions {
    display: flex;
    align-items: center;
}

/* 弹框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal-content {
    background: white;
    border-radius: 20upx;
    width: 90%;
    max-width: 600upx;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30upx;
    border-bottom: 1px solid #eee;
}

.modal-title {
    font-size: 32upx;
    font-weight: bold;
    color: #333;
}

.modal-header .cuIcon-close {
    font-size: 40upx;
    cursor: pointer;
}

.modal-body {
    padding: 30upx;
}

.form-group {
    position: relative;
}

.comment-textarea {
    width: 100%;
    min-height: 200upx;
    padding: 20upx;
    border: 1px solid #ddd;
    border-radius: 10upx;
    font-size: 28upx;
    line-height: 1.6;
    resize: none;
    box-sizing: border-box;
}

.comment-textarea:focus {
    border-color: #07c160;
    outline: none;
}

.char-count {
    position: absolute;
    bottom: 10upx;
    right: 15upx;
    font-size: 24upx;
    color: #999;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 30upx;
    border-top: 1px solid #eee;
    gap: 20upx;
}
</style>

{
    "name" : "SPMS绩效管理系统",
    "appid" : "__UNI__09ACC2B",
    "description" : "SPMS绩效管理系统",
    "versionName" : "2.2.22",
    "versionCode" : 233,
    "transformPx" : false,
    "app-plus" : {
        /* 5+App特有相关 */
        "modules" : {
            "Maps" : {},
            "Push" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {},
            /* ios打包配置 */
            "sdkConfigs" : {
                "ad" : {},
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "87f4d2a4a0c42e0c86cf312c8b8154e8",
                        "appkey_android" : "87f4d2a4a0c42e0c86cf312c8b8154e8"
                    }
                },
                "push" : {
                    "unipush" : {}
                }
            }
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        "appid" : "wx3c2e87bb2e0f524d",
        "setting" : {
            "urlCheck" : false,
            "es6" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "有定位功能需要导航定位"
            }
        }
    },
    "h5" : {
        "title" : "SPMS绩效管理系统",
        "domain" : "pmsapp.colori.com",
        "devServer" : {
            "port" : 6688
        },
        "template" : "",
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "bf98482bb3a3ee9c97a371ffe8fbc9c2",
                    "securityJsCode" : "",
                    "serviceHost" : ""
                }
            }
        },
        "router" : {
            "mode" : "hash"
        }
    }
}

<template>
    <view class="bg-white" style="height: 100vh;">
        <view style="flex: 1; padding: 10px; position: relative;">
            <!-- :src="videoUrl" -->
            <!-- 视频预览 -->
            1
            <video v-if="previewUrl" style="width: 100%; height: 100%; border-radius: 5px;" :src="previewUrl" controls>
            </video>
        </view>
    </view>
</template>

<script>


export default {
    data() {
        return {
            previewUrl: "",
        }
    },
    onLoad: async function () {
        const urlParams = this.$Route.query
        console.log("🚀 ~ onLoad ~ urlParams:", urlParams)
        let res = await this.$http.get('/pm/clockIn/previewPic?objectKey=' + urlParams.file)
        this.previewUrl = res.data.result;
    },
    methods: {

    }
}
</script>

<style></style>

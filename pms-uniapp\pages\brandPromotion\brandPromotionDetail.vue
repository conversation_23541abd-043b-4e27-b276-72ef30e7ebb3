<template>
    <view class="bg-white" style="height: 100vh;">
        <view style="flex: 1; padding: 10px; position: relative;">
            <!-- 视频播放器 -->
            <video v-if="previewUrl" style="width: 100%; height: 100%; border-radius: 5px;" :src="previewUrl" controls
                :poster="videoPoster">
            </video>


            <!-- 加载状态 -->
            <view v-if="!previewUrl" class="loading-container">
                <text>加载中...</text>
            </view>
        </view>
    </view>
</template>

<script>


export default {
    data() {
        return {
            previewUrl: "",
            videoPoster: "", // 视频封面图
        }
    },
    onLoad: async function () {
        const urlParams = this.$Route.query
        // 判断文件类型
        let res = await this.$http.get('/pm/clockIn/previewPic?objectKey=' + urlParams.file)
        this.previewUrl = res.data.result;
        console.log("🚀 ~   this.previewUrl :", this.previewUrl)
    },
    methods: {
        // 图片加载错误处理
        onImageError(e) {
            console.error('图片加载失败:', e);
            uni.showToast({
                title: '图片加载失败',
                icon: 'none'
            });
        },

        // 图片加载成功处理
        onImageLoad(e) {
            console.log('图片加载成功:', e);
        }
    }
}
</script>

<style scoped>
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #999;
    font-size: 16px;
}

/* 视频样式 */
video {
    object-fit: contain;
    background-color: #000;
}

/* 图片样式 */
image {
    object-fit: contain;
    background-color: #f5f5f5;
}

/* 响应式调整 */
@media (max-width: 750rpx) {

    video,
    image {
        border-radius: 3px;
    }
}
</style>

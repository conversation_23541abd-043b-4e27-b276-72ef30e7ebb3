<template>
    <view class="bg-white" style="height: 100vh;">
        <view style="flex: 1; padding: 10px; position: relative;">
            {{ previewUrl }}
            <!-- 视频播放器 -->
            <video
                v-if="previewUrl && isVideo"
                style="width: 100%; height: 100%; border-radius: 5px;"
                :src="previewUrl"
                controls
                :poster="videoPoster"
            >
            </video>

            <!-- 图片显示 -->
            <image
                v-if="previewUrl && !isVideo"
                style="width: 100%; height: 100%; border-radius: 5px;"
                :src="previewUrl"
                mode="aspectFit"
                @error="onImageError"
                @load="onImageLoad"
            >
            </image>


            <!-- 加载状态 -->
            <view v-if="!previewUrl" class="loading-container">
                <text>加载中...</text>
            </view>

            <!-- 调试信息 -->
            <view class="debug-info" style="position: absolute; top: 0; left: 0; background: rgba(0,0,0,0.7); color: white; padding: 10px; font-size: 12px; z-index: 999;">
                <text>调试信息:</text><br>
                <text>previewUrl: {{ previewUrl ? '已获取' : '未获取' }}</text><br>
                <text>isVideo: {{ isVideo }}</text><br>
                <text>fileType: {{ fileType }}</text><br>
                <text>显示组件: {{ previewUrl && isVideo ? 'video' : (previewUrl && !isVideo ? 'image' : 'none') }}</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            previewUrl: "",
            isVideo: false,
            videoPoster: "", // 视频封面图
            fileType: "", // 文件类型
        }
    },
    onLoad: async function () {
        try {
            const urlParams = this.$Route.query
            console.log("🚀 ~ onLoad ~ urlParams:", urlParams)

            // 检查参数
            if (!urlParams.file) {
                console.error('缺少文件参数');
                uni.showToast({
                    title: '缺少文件参数',
                    icon: 'none'
                });
                return;
            }

            // 判断文件类型
            this.determineFileType(urlParams.file);

            console.log('开始请求预览URL...');
            let res = await this.$http.get('/pm/clockIn/previewPic?objectKey=' + urlParams.file)
            console.log('API响应:', res);

            if (res && res.data && res.data.result) {
                this.previewUrl = res.data.result;
                console.log('预览URL设置成功:', this.previewUrl);
            } else {
                console.error('API响应格式错误:', res);
                uni.showToast({
                    title: '获取预览失败',
                    icon: 'none'
                });
            }
        } catch (error) {
            console.error('onLoad错误:', error);
            uni.showToast({
                title: '加载失败: ' + error.message,
                icon: 'none'
            });
        }
    },
    methods: {
        // 判断文件类型
        determineFileType(fileName) {
            if (!fileName) return;

            const fileExtension = fileName.toLowerCase().split('.').pop();

            // 视频文件扩展名
            const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'm4v', '3gp', 'mkv'];
            // 图片文件扩展名
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];

            if (videoExtensions.includes(fileExtension)) {
                this.isVideo = true;
                this.fileType = 'video';
            } else if (imageExtensions.includes(fileExtension)) {
                this.isVideo = false;
                this.fileType = 'image';
            } else {
                // 默认当作图片处理
                this.isVideo = false;
                this.fileType = 'unknown';
                console.warn('未知文件类型:', fileExtension);
            }

            console.log('文件类型判断:', {
                fileName,
                fileExtension,
                isVideo: this.isVideo,
                fileType: this.fileType
            });
        },

        // 图片加载错误处理
        onImageError(e) {
            console.error('图片加载失败:', e);
            uni.showToast({
                title: '图片加载失败',
                icon: 'none'
            });
        },

        // 图片加载成功处理
        onImageLoad(e) {
            console.log('图片加载成功:', e);
        }
    }
}
</script>

<style scoped>
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #999;
    font-size: 16px;
}

/* 视频样式 */
video {
    object-fit: contain;
    background-color: #000;
}

/* 图片样式 */
image {
    object-fit: contain;
    background-color: #f5f5f5;
}

/* 响应式调整 */
@media (max-width: 750rpx) {

    video,
    image {
        border-radius: 3px;
    }
}
</style>

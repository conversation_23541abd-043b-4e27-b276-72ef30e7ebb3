{
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style" :
			{
				"navigationBarTitleText" : "SPMS系统"
			}
		},
		{
			"path": "pages/login/login",
			"style" :
			{
				"navigationBarTitleText" : "登录"
			}
		}, 
		{
			"path": "pages/login/loginOauth2",
			"style" :
			{
				"navigationBarTitleText" : "登录"
			}
		},
		
		{
			"path": "pages/basics/layout",
			"style": {}
		},
		{
			"path": "pages/basics/background",
			"style": {}
		},
		{
			"path": "pages/basics/text",
			"style": {}
		},
		{
			"path": "pages/basics/icon",
			"style": {}
		},
		{
			"path": "pages/basics/button",
			"style": {}
		},
		{
			"path": "pages/basics/design",
			"style": {}
		},
		{
			"path": "pages/basics/tag",
			"style": {}
		},
		{
			"path": "pages/basics/avatar",
			"style": {}
		},
		{
			"path": "pages/basics/progress",
			"style": {}
		},
		{
			"path": "pages/basics/shadow",
			"style": {}
		},
		{
			"path": "pages/basics/loading",
			"style": {}
		},
		{
			"path": "pages/component/bar",
			"style": {}
		},
		{
			"path": "pages/component/nav",
			"style": {}
		},
		{
			"path": "pages/component/list",
			"style": {}
		},
		{
			"path": "pages/component/card",
			"style": {}
		},
		{
			"path": "pages/component/form",
			"style": {}
		},
		{
			"path": "pages/component/timeline",
			"style": {}
		},
		{
			"path": "pages/component/chat",
			"style": {}
		},
		{
			"path": "pages/component/swiper",
			"style": {}
		},
		{
			"path": "pages/component/modal",
			"style": {}
		},
		{
			"path": "pages/component/steps",
			"style": {}
		}, {
			"path": "pages/plugin/indexes",
			"style": {}
		}, {
			"path": "pages/plugin/animation",
			"style": {}
		}, {
			"path": "pages/plugin/drawer",
			"style": {}
		}, {
			"path": "pages/plugin/verticalnav",
			"style": {}
		}, {
			"path": "pages/home/<USER>",
			"style": {}
		}, {
			"path": "pages/user/userexit",
			"style": {}
		}, {
			"path": "pages/user/userdetail",
			"style": {}
		},{
			"path": "pages/user/useredit",
			"style": {}
		}, {
			"path": "pages/user/people",
			"style": {}
		}, {
			"path": "pages/common/exit",
			"style": {}
		}, {
			"path": "pages/common/success",
			"style": {}
		},{
			"path": "pages/user/location",
			"style": {},
			"permission": {
				"scope.userLocation": {
				  "desc": "你的位置信息将用于小程序位置接口的效果展示" // 高速公路行驶持续后台定位
				}
			  }
		},{
			"path": "pages/addressbook/address-book",
			"style": {}
		},{
			"path": "pages/addressbook/level-address-book",
			"style": {}
		},
		{
			"path": "pages/addressbook/member",
			"style": {}
		},{
			"path": "pages/addressbook/address-detail",
			"style": {}
		},{
			"path": "pages/annotation/annotationList",
			"style": {
				"app-plus" : {
					"bounce" : "none" //删除此项: mescroll-body支持iOS回弹
				}
			}
		},{
			"path": "pages/annotation/annotationDetail",
			"style": {}
		},{
			"path": "pages/common/helloWorld",
			"style": {}
		},
		{
			"path" : "pages/userInfo/userInfo",
			"style" : 
			{
				"navigationBarTitleText" : "人员信息"
			}
		},
		{
			"path" : "pages/customerInfo/customerInfo",
			"style" : 
			{
				"navigationBarTitleText" : "客户信息"
			}
		},
		{
			"path" : "pages/payData/payData",
			"style" : 
			{
				"navigationBarTitleText" : "回款数据"
			}
		},
		{
			"path" : "pages/salesData/salesData",
			"style" : 
			{
				"navigationBarTitleText" : "发货数据"
			}
		},
		{
			"path" : "pages/userInfo/userInfoDetail",
			"style" : 
			{
				"navigationBarTitleText" : "人员详情"
			}
		},
		{
			"path" : "pages/customerInfo/customerInfoDetail",
			"style" : 
			{
				"navigationBarTitleText" : "客户详情"
			}
		},
		{
			"path" : "pages/travelExpense/travelExpense",
			"style" : 
			{
				"navigationBarTitleText" : "差旅费"
			}
		},
		{
			"path" : "pages/trackingReport/trackingReport",
			"style" : 
			{
				"navigationBarTitleText" : "每日项目跟踪"
			}
		},
		{
			"path" : "pages/trackingReport/trackingReportDetail",
			"style" : 
			{
				"navigationBarTitleText" : "每日项目跟踪"
			}
		},
		{
			"path" : "pages/salesDataEntry/salesDataEntry",
			"style" : 
			{
				"navigationBarTitleText" : "销售数据录入"
			}
		},
		{
			"path" : "pages/opocUserList/opocUserList",
			"style" : 
			{
				"navigationBarTitleText" : "促销员列表"
			}
		},
		{
			"path" : "pages/signIn/signIn",
			"style" : 
			{
				"navigationBarTitleText" : "签到"
			}
		},
		{
			"path" : "pages/dailyWorkReport/dailyWorkReport",
			"style" : 
			{
				"navigationBarTitleText" : "工作日报"
			}
		},
		{
			"path" : "pages/workMoments/workMoments",
			"style" : 
			{
				"navigationBarTitleText" : "工作朋友圈"
			}
		},
		{
			"path" : "pages/workMoments/workMomentsPersonal",
			"style" : 
			{
				"navigationBarTitleText" : "个人朋友圈"
			}
		},
		{
			"path" : "pages/customerVisit/customerVisit",
			"style" : 
			{
				"navigationBarTitleText" : "客户拜访"
			}
		},
		{
			"path" : "pages/customerVisit/visitDetail",
			"style" : 
			{
				"navigationBarTitleText" : "拜访计划"
			}
		},
		{
			"path" : "pages/salerList/salerList",
			"style" : 
			{
				"navigationBarTitleText" : "业务员列表"
			}
		},
		{
			"path" : "pages/costList/costList",
			"style" : 
			{
				"navigationBarTitleText" : "费用申请"
			}
		},
		{
			"path" : "pages/brandPromotion/brandPromotion",
			"style" : 
			{
				"navigationBarTitleText" : "品牌宣传"
			}
		},
		{
			"path" : "pages/brandPromotion/brandPromotionDetail",
			"style" : 
			{
				"navigationBarTitleText" : "品牌宣传"
			}
		},
		{
			"path" : "pages/SFASummary/SFASummary",
			"style" : 
			{
				"navigationBarTitleText" : "每日情况汇总"
			}
		},
		{
			"path" : "pages/SFASummary/SFASummaryDetail",
			"style" : 
			{
				"navigationBarTitleText" : "每日情况汇总详情"
			}
		}
		
	],
	"globalStyle": {
		"mp-alipay": {
			/* 支付宝小程序特有相关 */
			"transparentTitle": "always",
			"allowsBounceVertical": "NO"
		},
		"navigationBarBackgroundColor": "#0081ff",
		"navigationBarTitleText": "JEECG BOOT",
		"navigationStyle": "custom",
		"navigationBarTextStyle": "white"
	},
	"usingComponts": true,
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "表单", //模式名称
			"path": "pages/index/index", //启动页面
			"query": "" //启动参数
		}]
	}

}
